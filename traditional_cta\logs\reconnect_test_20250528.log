2025-05-28 16:50:59,141 - INFO - 🚀 启动WebSocket重连功能测试...
2025-05-28 16:50:59,142 - INFO - 📅 测试时间: 2025-05-28 16:50:59
2025-05-28 16:50:59,142 - INFO - ============================================================
2025-05-28 16:50:59,143 - INFO - 
⚙️ 测试重连配置验证...
2025-05-28 16:50:59,143 - INFO - ✅ 最大重试次数: 有效
2025-05-28 16:50:59,143 - INFO - ✅ 初始延迟: 有效
2025-05-28 16:50:59,143 - INFO - ✅ 最大延迟: 有效
2025-05-28 16:50:59,144 - INFO - ✅ 退避因子: 有效
2025-05-28 16:50:59,144 - INFO - ✅ 心跳间隔: 有效
2025-05-28 16:50:59,144 - INFO - ✅ 心跳超时: 有效
2025-05-28 16:50:59,144 - INFO - ✅ 所有配置参数验证通过
2025-05-28 16:50:59,144 - INFO - 🧪 开始测试WebSocket重连逻辑...
2025-05-28 16:50:59,145 - INFO - ==================================================
2025-05-28 16:50:59,145 - INFO - 第 1 次重连延迟: 2.00 秒
2025-05-28 16:50:59,145 - INFO - 第 2 次重连延迟: 3.00 秒
2025-05-28 16:50:59,145 - INFO - 第 3 次重连延迟: 4.50 秒
2025-05-28 16:50:59,146 - INFO - 第 4 次重连延迟: 6.75 秒
2025-05-28 16:50:59,146 - INFO - 第 5 次重连延迟: 10.00 秒
2025-05-28 16:50:59,146 - INFO - ✅ 重连逻辑测试完成
2025-05-28 16:50:59,146 - INFO - 
🔄 模拟连接断开和重连过程...
2025-05-28 16:50:59,147 - INFO - 🟢 步骤 1: 连接建立
2025-05-28 16:51:00,147 - INFO - 🟢 步骤 2: 正常运行
2025-05-28 16:51:01,148 - INFO - 🔴 步骤 3: 网络波动
2025-05-28 16:51:02,149 - INFO - 🔴 步骤 4: 重连中
2025-05-28 16:51:03,150 - INFO - 🟢 步骤 5: 连接恢复
2025-05-28 16:51:04,150 - INFO - 🔴 步骤 6: 再次断开
2025-05-28 16:51:05,151 - INFO - 🟢 步骤 7: 重连成功
2025-05-28 16:51:06,152 - INFO - ✅ 连接模拟测试完成
2025-05-28 16:51:06,152 - INFO - 
🎉 所有测试完成!
2025-05-28 16:51:06,152 - INFO - 📋 测试总结:
2025-05-28 16:51:06,153 - INFO -    ✅ 配置验证: 通过
2025-05-28 16:51:06,153 - INFO -    ✅ 重连逻辑: 通过
2025-05-28 16:51:06,154 - INFO -    ✅ 连接模拟: 通过
2025-05-28 16:51:06,154 - INFO - 🛑 测试结束
