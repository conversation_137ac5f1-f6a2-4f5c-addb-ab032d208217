from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest
from factor_evaluation.data_service import DataService
import pandas as pd
import datetime

# df = pd.read_csv(
#     r'C:\Users\<USER>\python sc\example\回测结果250411_1.csv')  # 15m

# df = df.set_index('Unnamed: 0').rename_axis('open_time', axis=0)
# df.index = pd.to_datetime(df.index)  # index必须是datetime类型

ds = DataService()
currency = 'ETHUSDT_15m_2020_2025'

signals_list = ["ret_stc_sig_price",
                "ret_hv_ratio_signals",
                "ret_td_signals",
                "ret_ao_signals",
                "ret_ena_signals",
                "ret_williams_r_sig_price",
                "ret_momentum_sig_price",
                "ret_kc_strategy",
                "ret_bollinger_rsi_signals",
                "ret_macd_sig_price",
                "ret_ma_arrangement_sig",
                "ret_ma20_ma120_cross_sig_price",
                "ret_rsi_ma120_cross_sig_price",
                "ret_ma120_macd_1_cross_sig_price",
                "ret_ma120_bolling_cross_sig_price",
                "ret_ma120_cci_cross_sig_price",
                "ret_macd_02_cross_sig_price",
                "ret_ma120_macd_02_cross_sig_price",
                "ret_cci_fibonacci_signals",
                "ret_ma20_volume_cross_signals",
                "ret_ma20_rsi_macd_cross_sig_price",
                "ret_ma50_cross_sig_price",
                "ret_ma_bbi_rsi_sig_price",
                "ret_dc_bbi_cross_sig_price",
                "ret_ma_cci_sig",
                "ret_ma_vol_cci_sig",
                "ret_ma_short_long_cross_sig_price",
                "ret_ma_atr_cross_sig_price",
                "ret_dpo_ma_cross_sig_price",
                "ret_po_signals",
                "ret_rma_cross_sig_price",
                "ret_ma120_bbi_signals",
                "ret_skdj_sig_price",
                "ret_vao_signals",
                "ret_wma_signals",
                "ret_rsi_bb_ma_signal",
                "ret_macd_cross_signal",
                "ret_rsi_boll_sig",
                "ret_mfi_sig_price",
                ]
factor_name = 'ret_ena_signals'
df = ds[currency]
df['signals'] = ds[currency, factor_name]
df = df['2021-09-23':]
engine = run_multi_position_strategy_backtest(
    df=df,
    holding_bars=96,
    position_size=1,       # 数量或金额
    rate=0.0003,
    slippage=0.01,
    capital=50000,
    order_type="quantity",  # 可选 "quantity"(按币数量) 或 "amount"(按金额)
    plot_show=False,
    plot_save=False,
)

stat = engine.calculate_statistics()
stat['factor_name'] = factor_name
print('='*40)
print(pd.DataFrame([stat]))
