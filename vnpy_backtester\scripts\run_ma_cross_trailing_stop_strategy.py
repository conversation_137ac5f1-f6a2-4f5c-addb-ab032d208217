"""
运行MA交叉策略回测（带移动止损）
使用MA10上穿MA20反手做多，下穿反手做空，不使用固定持仓K线数量
增加移动止损功能：
1. 每次开仓后，设置3%(参数可调)的止盈止损线
2. 当跌破止损线后，立即止损
3. 涨破止盈线后，不止盈，记录K线最高价(每根K线都重新记录)和当前止盈价
4. 当利润回撤到最高价和止盈线之间60%时，进行止盈
"""

import pandas as pd
import numpy as np
from datetime import datetime
import traceback

from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.constant import Exchange
from vnpy_backtester.utils.chart_engine import PlotlyChartEngine


def run_ma_cross_trailing_stop_strategy_backtest(df=None, data_file=None, fast_window=10, slow_window=20, position_size=10, rate=0.0003, slippage=0.001, capital=50000, plot_show=True, plot_save=True, debug=False, order_type="quantity", stop_loss_pct=3.0, profit_take_ratio=0.6, **strategy_params):
    """
    运行MA交叉策略回测（带移动止损）

    参数:
    df: 数据DataFrame,如果为None则从data_file加载
    data_file: 数据文件路径,仅在df为None时使用
    fast_window: 快速均线窗口,默认为10
    slow_window: 慢速均线窗口,默认为20
    position_size: 每次开仓的数量、金额或资金比例,默认为10
                  当order_type="quantity"时表示币的数量
                  当order_type="amount"时表示开仓金额
                  当order_type="ratio"时表示资金比例(0.1表示10%)
    rate: 手续费率,默认为0.0003
    slippage: 滑点,默认为0.001
    capital: 初始资金,默认为50000
    plot_show: 是否在浏览器中显示图表,默认为True
    plot_save: 是否保存图表文件,默认为True
    debug: 是否开启调试模式,默认为False
    order_type: 下单方式,可选"quantity"(按币数量)、"amount"(按金额)或"ratio"(按资金比例),默认为"quantity"
    stop_loss_pct: 止损百分比,默认为3.0%
    profit_take_ratio: 回撤比例,当利润回撤到最高价和止盈线之间指定比例时止盈,默认为0.6(60%)
    **strategy_params: 其他策略参数

    DataFrame要求:
    传入的DataFrame必须包含以下列:
    - open: float类型,开盘价
    - high: float类型,最高价
    - low: float类型,最低价
    - close: float类型,收盘价
    - volume: float类型,成交量

    DataFrame索引要求:
    - 索引应为datetime类型,表示每个K线的时间
    - 如果索引不是datetime类型,函数会尝试使用'datetime'列作为索引
    - 如果没有'datetime'列,会创建一个简单的日期索引
    """
    # 加载数据
    print("加载数据文件...")

    print(f"成功加载数据,共 {len(df)} 条记录")

    # 检查必要的列是否存在
    required_columns = ["open", "high", "low", "close", "volume"]
    missing_columns = [
        col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 数据中缺少必要的列: {missing_columns}")
        return

    # 检查数据类型
    try:
        # 检查价格和成交量列是否可以转换为float类型
        for col in ["open", "high", "low", "close", "volume"]:
            df[col] = df[col].astype(float)
    except Exception as e:
        print(f"错误: 数据类型转换失败: {e}")
        return

    # 检查索引是否为datetime类型
    if not isinstance(df.index, pd.DatetimeIndex):
        print("警告: 索引不是datetime类型,尝试使用'datetime'列作为索引")
        if "datetime" in df.columns:
            try:
                df.index = pd.to_datetime(df["datetime"])
                print("成功使用'datetime'列作为索引")
            except Exception as e:
                print(f"错误: 无法将'datetime'列转换为日期索引: {e}")
                print("创建一个简单的日期索引")
                start_date = datetime(2021, 1, 1)
                df.index = pd.date_range(
                    start=start_date, periods=len(df), freq="15min")
        else:
            print("警告: 没有'datetime'列,创建一个简单的日期索引")
            start_date = datetime(2021, 1, 1)
            df.index = pd.date_range(
                start=start_date, periods=len(df), freq="15min")

    # 创建Bar对象列表
    print("准备回测数据...")
    bars = []

    # 使用pandas的itertuples方法,比iterrows更高效
    for row in df.itertuples():
        # 获取行的索引(日期时间)
        dt = row.Index

        # 创建BarData对象
        bar = BarData(
            symbol="ETHUSDT",
            exchange=Exchange.BINANCE,
            datetime=dt,  # 使用原始的datetime对象
            open_price=row.open,
            high_price=row.high,
            low_price=row.low,
            close_price=row.close,
            volume=row.volume,
            # 如果没有turnover列,使用volume*close
            turnover=getattr(row, "turnover", row.volume * row.close),
            gateway_name="BACKTEST"
        )
        bars.append(bar)

    # 设置回测引擎
    if order_type == "quantity":
        position_desc = f"每次开仓数量: {position_size}"
    elif order_type == "amount":
        position_desc = f"每次开仓金额: {position_size}"
    elif order_type == "ratio":
        position_desc = f"每次开仓资金比例: {position_size * 100:.2f}%"
    else:
        position_desc = f"每次开仓数量: {position_size}"

    print(
        f"设置回测引擎... (快速均线: {fast_window}, 慢速均线: {slow_window}, {position_desc}, 下单方式: {order_type}, 止损百分比: {stop_loss_pct}%, 回撤止盈比例: {profit_take_ratio*100}%)")
    engine = BacktestingEngine()

    # 合并所有参数
    params = {
        "vt_symbol": "ETHUSDT.BINANCE",
        "start": df.index[0],
        "end": df.index[-1],
        "rate": rate,  # 手续费率
        "slippage": slippage,  # 滑点
        "capital": capital,  # 初始资金
        "fast_window": fast_window,  # 快速均线窗口
        "slow_window": slow_window,  # 慢速均线窗口
        "position_size": position_size,  # 每次开仓数量或金额
        "debug_mode": debug,  # 调试模式
        "order_type": order_type,  # 下单方式：按币数量或按金额
        "stop_loss_pct": stop_loss_pct,  # 止损百分比
        "profit_take_ratio": profit_take_ratio  # 回撤止盈比例
    }

    # 添加其他策略参数
    if strategy_params:
        params.update(strategy_params)

    # 设置参数
    engine.set_parameters(**params)

    # 添加策略
    print("添加策略...")
    from vnpy_backtester.strategies.ma_cross_trailing_stop_strategy import MACrossTrailingStopStrategy
    engine.add_strategy(MACrossTrailingStopStrategy)

    # 加载数据
    engine.history_data = bars

    # 运行回测
    print("开始回测...")

    engine.run_backtesting()

    # 计算结果
    engine.calculate_result()

    # 显示统计指标
    stats = engine.calculate_statistics()
    print("\n====== 回测结果 ======")
    print(f"策略: MACrossTrailingStopStrategy")
    print(f"开始日期: {stats['start_date']}  结束日期: {stats['end_date']}")
    print(
        f"总交易日: {stats['total_days']}  盈利日: {stats['profit_days']}  亏损日: {stats['loss_days']}")
    print(f"初始资金: {stats['capital']:,.2f}  结束资金: {stats['end_balance']:,.2f}")
    print(f"总收益率: {stats['total_return']:,.2f}%")
    print(
        f"最大回撤: {stats['max_drawdown']:,.2f}%  夏普比率: {stats['sharpe_ratio']:,.2f}")

    # 输出交易统计
    if 'total_trades' in stats:
        print(
            f"总交易次数: {stats['total_trades']}  盈利交易: {stats['winning_trades']}  亏损交易: {stats['losing_trades']}")
        if 'profit_ratio' in stats:
            print(
                f"胜率: {stats['win_rate']:,.2f}%  盈亏比: {stats['profit_ratio']:,.2f}")
        else:
            print(
                f"胜率: {stats['win_rate']:,.2f}%  利润因子: {stats['profit_factor']:,.2f}")
        if 'avg_winning' in stats and 'avg_losing' in stats:
            print(
                f"平均盈利: {stats['avg_winning']:,.2f}  平均亏损: {stats['avg_losing']:,.2f}")

        # 只显示总盈亏
        balance_pnl = stats['end_balance'] - stats['capital']
        print(f"总盈亏: {balance_pnl:,.2f}")

    # 使用PlotlyChartEngine显示资金曲线
    if plot_show or plot_save:  # 只有在需要显示或保存图表时才创建图表
        try:
            # 创建图表引擎
            chart_engine = PlotlyChartEngine()

            # 获取初始资金
            initial_capital = stats['capital']

            # 创建图表文件路径
            if order_type == "quantity":
                order_type_str = "quantity"
            elif order_type == "amount":
                order_type_str = "amount"
            elif order_type == "ratio":
                order_type_str = f"ratio_{int(position_size * 100)}pct"
            else:
                order_type_str = "quantity"

            chart_file_path = f"vnpy_backtester/charts/ma_cross_trailing_stop_strategy_result_{fast_window}_{slow_window}_{position_size}_{stop_loss_pct}_{profit_take_ratio*100}_{order_type_str}.html"

            # 根据参数决定是否显示和保存图表
            save_path = chart_file_path if plot_save else None

            # 创建图表标题
            position_desc = f"每次开仓数量: {position_size}" if order_type == "quantity" else f"每次开仓金额: {position_size}"
            title = f"MA交叉策略(带移动止损)回测结果 (MA{fast_window}/MA{slow_window}, {position_desc})"

            # 创建图表
            chart_engine.create_chart(
                engine=engine,
                title=title,
                save_path=save_path,
                show=plot_show,
                initial_capital=initial_capital
            )

            # 如果保存了图表,显示保存信息
            if plot_save:
                print(f"图表已保存为 {chart_file_path}")

        except Exception as e:
            print(f"图表处理时出错: {e}")
            traceback.print_exc()
            if plot_show:
                try:
                    # 如果Plotly出错,尝试使用原始的show_chart方法
                    engine.show_chart()
                except Exception as e2:
                    print(f"原始图表显示也出错: {e2}")
    else:
        print("图表未显示和保存 (plot_show=False, plot_save=False)")

    return engine


if __name__ == "__main__":
    # 传入数据
    df = pd.read_csv(
        r'C:\Users\<USER>\python sc\example\回测结果250411_1.csv')  # 15m

    # df = pd.read_csv(
    #     r'C:\Users\<USER>\python sc\example\250416test02.csv')  # 1h

    df = df.set_index('Unnamed: 0').rename_axis('open_time', axis=0)
    df.index = pd.to_datetime(df.index)  # index必须是datetime类型

    from factor_evaluation.data_service import DataService
    ds = DataService()
    df = ds['ETHUSDT_15m_2020_2025']
    df = df['2021-10-1':]
    # df.to_pickle(
    #     r'C:\Users\<USER>\python sc\traditional_cta\df_temp.pkl')
    split_point = int(len(df) * 0.7)
    train = df.iloc[:split_point].copy()  # 前70%
    test = df.iloc[split_point:].copy()   # 后30%

    # 运行回测程序 - 按币数量下单
    engine = run_ma_cross_trailing_stop_strategy_backtest(
        df=df,
        fast_window=5,
        slow_window=100,
        position_size=30000,
        rate=0.0003,
        slippage=0.01,
        capital=50000,
        # 可选 "quantity"(按币数量)、"amount"(按金额)
        order_type="amount",
        stop_loss_pct=2.0,      # 止损百分比，默认为3.0%
        profit_take_ratio=0.8,  # 回撤止盈比例，默认为0.6(60%)
        plot_save=False,
    )
